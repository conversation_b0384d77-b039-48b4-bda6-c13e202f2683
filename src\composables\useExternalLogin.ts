import { onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { useSettingStore } from '@/store/modules/setting'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { resetRouteRegistration } from '@/router'
import { login, getUserInfo } from '@/api/usersApi'

/**
 * 外部登录通信组合函数
 * 用于处理来自外部页面的登录消息
 */
export function useExternalLogin() {
  const userStore = useUserStore()
  const settingStore = useSettingStore()
  const router = useRouter()

  /**
   * 处理自动登录请求（子组件完全负责登录流程）
   */
  const handleAutoLoginRequest = async (requestData: any) => {
    try {
      console.log('收到自动登录请求:', {
        targetPath: requestData.targetPath,
        autoNavigate: requestData.autoNavigate
      })

      const { targetPath, autoNavigate } = requestData

      // 子组件内部管理登录凭据
      const credentials = {
        username: 'admin',
        password: 'admin123'
      }

      console.log('使用内部凭据执行自动登录:', { username: credentials.username })

      // 执行自动登录
      const loginSuccess = await performAutoLogin(credentials)

      if (loginSuccess) {
        // 发送自动登录成功响应
        window.parent.postMessage({
          type: 'AUTO_LOGIN_SUCCESS',
          data: {
            success: true,
            message: '自动登录成功',
            targetPath: targetPath,
            autoNavigate: autoNavigate,
            timestamp: Date.now()
          }
        }, '*')

        return true
      } else {
        throw new Error('自动登录失败')
      }

    } catch (error: any) {
      console.error('自动登录请求处理失败:', error)

      // 发送自动登录失败响应
      window.parent.postMessage({
        type: 'AUTO_LOGIN_ERROR',
        data: {
          success: false,
          error: error.message,
          timestamp: Date.now()
        }
      }, '*')

      ElMessage.error('自动登录失败: ' + error.message)
      return false
    }
  }

  /**
   * 执行自动登录（子组件内部处理）
   */
  const performAutoLogin = async (credentials: any) => {
    try {
      console.log('执行自动登录:', { username: credentials.username })

      const { username, password } = credentials

      if (!username || !password) {
        throw new Error('缺少用户名或密码')
      }

      // 1. 预先设置外部登录标记，防止其他流程干扰
      localStorage.setItem('externalLogin', 'true')
      localStorage.setItem('externalLoginTime', Date.now().toString())
      console.log('✓ 外部登录标记已预设置')

      // 2. 调用登录API
      console.log('正在调用登录API...')
      const loginRes: any = await login({ username, password })

      if (loginRes.code !== 200 || !loginRes.token) {
        throw new Error(loginRes.msg || '登录失败')
      }

      console.log('✓ 登录API调用成功')

      // 3. 设置登录状态（先设置状态再设置token）
      userStore.setLoginStatus(true)
      console.log('✓ 登录状态已设置为true')

      // 4. 设置token（使用增强的存储机制）
      userStore.setToken(loginRes.token)
      console.log('✓ Token已设置')

      // 5. 等待token存储完成
      await new Promise(resolve => setTimeout(resolve, 100))

      // 6. 验证token存储
      const storedToken = localStorage.getItem('accessToken')
      if (!storedToken || storedToken !== loginRes.token) {
        console.warn('Token存储验证失败，重新存储')
        localStorage.setItem('accessToken', loginRes.token)
        userStore.setToken(loginRes.token)
      }

      // 7. 获取用户信息
      let userInfo = null
      try {
        const userRes: any = await getUserInfo()
        if (userRes.code === 200 && userRes.data) {
          userInfo = userRes.data
          userStore.setUserInfo(userInfo)
          console.log('✓ 用户信息已获取并设置:', userInfo)
        }
      } catch (error) {
        console.warn('获取用户信息失败，使用默认信息:', error)
        // 使用默认用户信息
        userInfo = {
          userId: 999999,
          userName: username,
          nickName: '管理员',
          admin: true
        }
        userStore.setUserInfo(userInfo)
      }

      // 8. 重置路由注册状态，允许重新获取路由
      resetRouteRegistration()
      console.log('✓ 路由注册状态已重置')

      // 9. 保存用户数据到localStorage（多次保存确保成功）
      userStore.saveUserData()
      await new Promise(resolve => setTimeout(resolve, 50))
      userStore.saveUserData()
      console.log('✓ 用户数据已保存到localStorage')

      // 10. 隐藏设置引导popover
      settingStore.hideSettingGuide()
      console.log('✓ 设置引导已隐藏')

      // 11. 最终验证存储状态
      const finalToken = localStorage.getItem('accessToken')
      const finalLoginStatus = userStore.isLogin
      console.log('✓ 最终状态验证:', {
        hasToken: !!finalToken,
        isLogin: finalLoginStatus,
        tokenMatch: finalToken === loginRes.token
      })

      console.log('✓ 自动登录流程完成')
    

      return true

    } catch (error: any) {
      console.error('自动登录失败:', error)

      // 清理外部登录标记
      localStorage.removeItem('externalLogin')
      localStorage.removeItem('externalLoginTime')

      ElMessage.error('自动登录失败: ' + error.message)
      return false
    }
  }

  /**
   * 处理外部登录消息（兼容旧版本）
   */
  const handleExternalLogin = async (loginData: any) => {
    try {
      console.log('处理外部登录数据:', loginData)

      const { token, userInfo } = loginData

      if (!token) {
        throw new Error('缺少登录token')
      }

      // 1. 设置token
      userStore.setToken(token)
      console.log('✓ Token已设置')

      // 2. 设置用户信息
      if (userInfo) {
        userStore.setUserInfo(userInfo)
        console.log('✓ 用户信息已设置:', userInfo)
      }

      // 3. 设置登录状态
      userStore.setLoginStatus(true)
      console.log('✓ 登录状态已设置为true')

      // 4. 重置路由注册状态，允许重新获取路由
      resetRouteRegistration()
      console.log('✓ 路由注册状态已重置')

      // 5. 保存用户数据到localStorage
      userStore.saveUserData()
      console.log('✓ 用户数据已保存到localStorage')

      // 6. 设置外部登录标记
      localStorage.setItem('externalLogin', 'true')
      localStorage.setItem('externalLoginTime', Date.now().toString())
      console.log('✓ 外部登录标记已设置')

      // 7. 隐藏设置引导popover
      settingStore.hideSettingGuide()
      console.log('✓ 设置引导已隐藏')

      // 8. 发送成功响应给外部页面
      window.parent.postMessage({
        type: 'LOGIN_SUCCESS_RESPONSE',
        data: {
          success: true,
          message: '登录成功',
          timestamp: Date.now()
        }
      }, '*')

      ElMessage.success('外部登录成功')
      console.log('✓ 外部登录流程完成')

      return true

    } catch (error: any) {
      console.error('外部登录处理失败:', error)
      
      // 发送错误响应给外部页面
      window.parent.postMessage({
        type: 'LOGIN_ERROR_RESPONSE',
        data: {
          success: false,
          error: error.message,
          timestamp: Date.now()
        }
      }, '*')

      ElMessage.error('外部登录失败: ' + error.message)
      return false
    }
  }

  /**
   * 处理页面跳转消息
   */
  const handleNavigation = async (navigationData: any) => {
    try {
      const { path } = navigationData

      if (!path) {
        throw new Error('缺少跳转路径')
      }

      console.log('处理页面跳转:', path)

      // 检查用户是否已登录
      if (!userStore.isLogin) {
        throw new Error('用户未登录，无法跳转')
      }

      // 执行路由跳转
      await router.push(path)
      console.log('✓ 页面跳转成功:', path)

      // 发送跳转成功响应
      window.parent.postMessage({
        type: 'NAVIGATION_SUCCESS',
        data: {
          success: true,
          path: path,
          timestamp: Date.now()
        }
      }, '*')

      return true

    } catch (error: any) {
      console.error('页面跳转失败:', error)
      
      // 发送跳转失败响应
      window.parent.postMessage({
        type: 'NAVIGATION_ERROR',
        data: {
          success: false,
          error: error.message,
          timestamp: Date.now()
        }
      }, '*')

      ElMessage.error('页面跳转失败: ' + error.message)
      return false
    }
  }

  /**
   * 处理来自外部页面的消息
   */
  const handleMessage = async (event: MessageEvent) => {
  
    const { type, data } = event.data

    console.log('收到外部消息:', { type, data })

    switch (type) {
      case 'AUTO_LOGIN_REQUEST':
        // 处理自动登录请求
        await handleAutoLoginRequest(data)
        break

      case 'EXTERNAL_LOGIN_REQUEST':
        // 兼容旧版本的外部登录凭据请求
        await performAutoLogin(data)
        break

      case 'EXTERNAL_LOGIN':
        // 兼容旧版本的直接token传递
        await handleExternalLogin(data)
        break

      case 'NAVIGATE_TO_PAGE':
        await handleNavigation(data)
        break

      case 'EXTERNAL_PAGE_READY':
        // 外部页面准备就绪，发送确认消息
        window.parent.postMessage({
          type: 'IFRAME_READY',
          data: {
            ready: true,
            timestamp: Date.now()
          }
        }, '*')
        console.log('已发送iframe准备就绪确认')
        break

      case 'IFRAME_READY_CHECK':
        // 响应iframe准备检查
        window.parent.postMessage({
          type: 'IFRAME_READY_RESPONSE',
          data: {
            ready: true,
            timestamp: Date.now()
          }
        }, '*')
        console.log('已响应iframe准备检查')
        break

      case 'CHECK_LOGIN_STATUS':
        // 检查当前登录状态
        const currentLoginStatus = {
          isLogin: userStore.isLogin,
          hasToken: !!userStore.accessToken,
          tokenFromStorage: !!localStorage.getItem('accessToken'),
          timestamp: Date.now()
        }

        window.parent.postMessage({
          type: 'LOGIN_STATUS_RESPONSE',
          data: currentLoginStatus
        }, '*')
        console.log('已发送登录状态检查响应:', currentLoginStatus)
        break

      default:
        console.log('未知消息类型:', type)
    }
  }

  /**
   * 初始化外部登录监听
   */
  const initExternalLogin = () => {
    // 添加消息监听器
    window.addEventListener('message', handleMessage)
    console.log('✓ 外部登录消息监听器已初始化')

    // 检查是否在iframe中
    if (window.parent !== window) {
      console.log('✓ 检测到运行在iframe中，外部登录功能已启用')
      
      // 向父页面发送准备就绪消息
      setTimeout(() => {
        window.parent.postMessage({
          type: 'IFRAME_READY',
          data: {
            ready: true,
            url: window.location.href,
            timestamp: Date.now()
          }
        }, '*')
      }, 1000)
    }
  }

  /**
   * 清理外部登录监听
   */
  const cleanupExternalLogin = () => {
    window.removeEventListener('message', handleMessage)
    console.log('✓ 外部登录消息监听器已清理')
  }

  // 生命周期钩子
  onMounted(() => {
    initExternalLogin()
  })

  onUnmounted(() => {
    cleanupExternalLogin()
  })

  return {
    handleAutoLoginRequest,
    performAutoLogin,
    handleExternalLogin,
    handleNavigation,
    initExternalLogin,
    cleanupExternalLogin
  }
}
